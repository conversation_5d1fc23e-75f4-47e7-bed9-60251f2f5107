package eve.sys.modular.test.dpvTestProgress.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import eve.core.pojo.page.PageResult;
import eve.sys.jiraModular.productManager.entity.ProductManager;
import eve.sys.jiraModular.productManager.service.IJIRAProductManagerService;
import eve.sys.limsModular.coreFile.entity.TCoreFile;
import eve.sys.limsModular.coreFile.service.ITCoreFileService;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.ordTask.entity.TLimsOrdtask;
import eve.sys.limsModular.ordTask.service.ITLimsOrdtaskService;
import eve.sys.modular.minio.service.MinioService;
import eve.sys.modular.test.dpvTestFailure.controller.FailureCellOcvRecordController;
import eve.sys.modular.test.dpvTestProgress.entity.TestProjectOutTestData;
import eve.sys.modular.test.dpvTestProgress.mapper.TestProjectOutTestDataMapper;
import eve.sys.modular.test.dpvTestProgress.param.TestProjectOutTestTableResult;
import eve.sys.modular.test.dpvTestProgress.service.ITestProjectOutTestDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 测试项目委外数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-01
 */
@Service
public class TestProjectOutTestDataServiceImpl extends ServiceImpl<TestProjectOutTestDataMapper, TestProjectOutTestData> implements ITestProjectOutTestDataService {

    @Resource
    private ITLimsFolderService folderService;

    @Resource
    private ITLimsOrdtaskService ordtaskService;

    @Resource
    private ITCoreFileService coreFileService;

    @Resource
    private IJIRAProductManagerService ijiraProductManagerService;

    @Autowired
    private FailureCellOcvRecordController failureCellOcvRecordController;

    @Resource
    private MinioService minioService;

    @Override
    public PageResult<TestProjectOutTestData> pageList(TestProjectOutTestData param) {
        LambdaQueryWrapper<TestProjectOutTestData> queryWrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(param.getFolderNo())){
            queryWrapper.like(TestProjectOutTestData::getFolderNo,param.getFolderNo());
        }
        if(StrUtil.isNotBlank(param.getProductName())){
            queryWrapper.like(TestProjectOutTestData::getProductName,param.getProductName());
        }
        if(StrUtil.isNotBlank(param.getTestReportCode())){
            queryWrapper.like(TestProjectOutTestData::getTestReportCode,param.getTestReportCode());
        }
        if(StrUtil.isNotBlank(param.getTestProjectName())){
            queryWrapper.like(TestProjectOutTestData::getTestProjectName,param.getTestProjectName());
        }

        queryWrapper.eq(TestProjectOutTestData::getHandDeleteStatus,0);

        queryWrapper.orderByDesc(TestProjectOutTestData::getFolderNo);

        Page<TestProjectOutTestData> page = this.page(new Page<>(param.getPageNo().longValue(), param.getPageSize().longValue()), queryWrapper);
        List<TestProjectOutTestData> recordList = page.getRecords();
        //计算有效期天数
        recordList.forEach(re -> {
            if(StrUtil.isBlank(re.getTestReportCode())){
                re.setCertificateStatus("申请中");
            }
            if(null != re.getPeriodValidity()){
                try {
                    // 将Date对象转换为LocalDate
                    LocalDate localDate1 = re.getPeriodValidity().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate localDate2 = new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    // 计算两个LocalDate之间的天数差
                    Long daysBetween = ChronoUnit.DAYS.between(localDate2, localDate1);
                    if(daysBetween <= 0){
                        re.setCertificateStatus("失效");
                        daysBetween = 0L;
                    }else{
                        re.setCertificateStatus("有效");
                    }
                    re.setRemainingTime(daysBetween.intValue());
                } catch (ExceptionInInitializerError e) {
                    log.error("计算时间错误",e);
                }
            }else{
                if(StrUtil.isNotBlank(re.getFileName())){
                    re.setCertificateStatus("有效");
                }
            }
        });
        return new PageResult(page);
    }

    @Override
    public void syncDataFromLims() {
        //查询第六实验室委外单
        LambdaQueryWrapper<TLimsFolder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TLimsFolder::getOutsourceflag,"1");
        queryWrapper.like(TLimsFolder::getLaboratory,"第六实验室");
        queryWrapper.in(TLimsFolder::getStatus, Arrays.asList("Demandreceive","Uploadnumber","Assign","Testassign","Testing","Reporting","Done","ConfirmResults"
                ,"ReportReview","ReportApproval","ReportReceive"));
        queryWrapper.orderByAsc(TLimsFolder::getFolderno);
        List<TLimsFolder> folderList = this.folderService.list(queryWrapper);
        //同步数据
        //查询委托单下所有测试项目
        folderList.forEach(f -> {
            String phase = "";
            if(StrUtil.isNotBlank(f.getTechnicalstatus()) && StrUtil.isNotBlank(f.getTechnicalstatusnumber())){
                phase = f.getTechnicalstatus().replace("样","") + f.getTechnicalstatusnumber() + "样";
            }
            LambdaQueryWrapper<TLimsOrdtask> ordTaskQueryWrapper= new LambdaQueryWrapper<>();
            ordTaskQueryWrapper.eq(TLimsOrdtask::getFolderid,f.getId());
            ordTaskQueryWrapper.eq(TLimsOrdtask::getSecondcategory,"认证");
            ordTaskQueryWrapper.orderByAsc(TLimsOrdtask::getSorter);
            List<TLimsOrdtask> ordtaskList = this.ordtaskService.list(ordTaskQueryWrapper);
            if(CollUtil.isNotEmpty(ordtaskList)){
                String finalPhase = phase;
                ordtaskList.forEach(ord -> {

                    LambdaQueryWrapper<ProductManager> productManagerLambdaQueryWrapper= new LambdaQueryWrapper<>();
                    productManagerLambdaQueryWrapper.like(ProductManager::getProductName,f.getProducttype());
                    List<ProductManager> list = ijiraProductManagerService.list(productManagerLambdaQueryWrapper);
                    String category = null;
                    if(CollUtil.isNotEmpty(list)){
                        category = list.get(0).getProductCateMultiName();
                    }

                    TestProjectOutTestData outTestData = TestProjectOutTestData.builder()
                            .costCenter(f.getCostcenter())
                            .costCenterCode(f.getCostcentercode())
                            .productName(f.getProducttype())
                            .classification(category)
                            .phase(finalPhase)
                            .testProjectName(ord.getMethodname())
                            .code(ord.getMethodname())
                            .secondCategory(ord.getSecondcategory())
                            .limsOrdtaskId(ord.getId())
                            .folderNo(f.getFolderno())
                            .folderStatus(f.getStatus())
                            .holder(f.getEntrusteunit())
                            .factory(f.getManufactunit())
                            .batteryCarrier(f.getCylindercellflag())
                            .fee(null != ord.getPrice() ? new BigDecimal(ord.getPrice()) : null)
                            .build();

                    LambdaQueryWrapper<TCoreFile> ordFileQueryWrapper= new LambdaQueryWrapper<>();
                    ordFileQueryWrapper.eq(TCoreFile::getTargetid,"T_LIMS_ORDTASK$"+ord.getId());
                    List<TCoreFile> coreFileList = this.coreFileService.list(ordFileQueryWrapper);


                    if(CollUtil.isNotEmpty(coreFileList)){
                        //取一个文件
                        TCoreFile coreFile = coreFileList.get(0);
                        outTestData.setFileName(coreFile.getName());
                        String yyyyMMdd = DateUtil.format(coreFile.getCreatedtime(), "yyyyMMdd");
                        outTestData.setFileUrl("http://lims.evebattery.com/files/secure/"+yyyyMMdd+"/"+coreFile.getMd5name()+"/"+coreFile.getName());
                    }



                    LambdaQueryWrapper<TestProjectOutTestData> outTestDataLambdaQueryWrapper= new LambdaQueryWrapper<>();
                    outTestDataLambdaQueryWrapper.eq(TestProjectOutTestData::getLimsOrdtaskId,ord.getId());
                    List<TestProjectOutTestData> outTestDataList = this.list(outTestDataLambdaQueryWrapper);

                    //更新
                    if(CollUtil.isNotEmpty(outTestDataList)){
                        outTestData.setId(outTestDataList.get(0).getId());
                        TestProjectOutTestData testProjectOutTestData = outTestDataList.get(0);
                        if(ObjectUtil.isNotEmpty(testProjectOutTestData.getFileId())){
                            outTestData.setFileUrl(null);
                            outTestData.setFileName(null);
                        }

                        //查询产品名称
                        ProductManager product = null;
                        if(StrUtil.isNotBlank(f.getProductid())){
                            product = ijiraProductManagerService.getById(f.getProductid());
                        }

                        String productName = outTestData.getProductName();

                        if(product != null && StrUtil.isNotBlank(product.getProductName()) && !product.getProductName().equals(outTestData.getProductName())){
                            outTestData.setProductName(product.getProductName());
                            productName = product.getProductName();
                        }else{
                            outTestData.setProductName(null);
                        }

                        LambdaQueryWrapper<ProductManager> productManagerLambdaQueryWrapper1= new LambdaQueryWrapper<>();
                        productManagerLambdaQueryWrapper1.like(ProductManager::getProductName,productName);
                        List<ProductManager> list1 = ijiraProductManagerService.list(productManagerLambdaQueryWrapper1);
                        String category1 = null;
                        if(CollUtil.isNotEmpty(list1)){
                            category1 = list1.get(0).getProductCateMultiName();
                        }

                        if(null != category1){
                            outTestData.setClassification(category1);
                        }
                        //上传文件到PBI
                        if(StrUtil.isNotBlank(outTestData.getFileUrl()) && null == outTestDataList.get(0).getFileId()){
                            try {
                                Long fileId = minioService.uploadFile("dpvproductcert", urlToMultipartFile(outTestData.getFileUrl(), outTestData.getFileName()), outTestData.getFileName(), null);
                                outTestData.setFileId(fileId);
                                outTestData.setFileIds(fileId.toString());
                            } catch (Exception e) {
                                log.error("产品认证文件上传失败",e);
                            }
                        }

                        this.updateById(outTestData);
                        //新增
                    }else{
                        outTestData.setCertificateStatus("申请中");
                        //上传文件到PBI
                        if(StrUtil.isNotBlank(outTestData.getFileUrl())){
                            try {
                                Long fileId = minioService.uploadFile("dpvproductcert", urlToMultipartFile(outTestData.getFileUrl(), outTestData.getFileName()), outTestData.getFileName(), null);
                                outTestData.setFileId(fileId);
                                outTestData.setFileIds(fileId.toString());
                            } catch (Exception e) {
                                log.error("产品认证文件上传失败",e);
                            }
                        }
                        this.save(outTestData);
                    }

                });
            }

        });
    }


    public MultipartFile urlToMultipartFile(String fileUrl, String fileName) throws IOException, URISyntaxException {

        // 进行URL编码，使用UTF-8字符集
        String encoded = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        // 将+替换为%20（因为URLEncoder.encode会将空格转为+）
        encoded = encoded.replace("+", "%20");
        String urlLink = fileUrl.replaceAll(fileName, encoded);

        // 1. 使用 URI 构造规范化的URL（自动编码特殊字符）
//        String baseUrl = fileUrl.replace(fileName, URLEncoder.encode(fileName, "UTF-8"));

        URL url = new URL(urlLink);
        String contentType = null;
        try (InputStream inputStream = url.openStream()) {
            // 读取文件的内容类型
            contentType = URLConnection.guessContentTypeFromStream(inputStream);
        }

        if(fileName.contains(".pdf") || fileName.contains(".PDF")){
            contentType = "application/pdf";
        }
        try (InputStream inputStream = url.openStream()) {

            // 如果无法自动检测内容类型，则可以手动指定
            if (contentType == null) {
                contentType = "application/octet-stream"; // 默认类型
            }
            // 使用 MockMultipartFile 创建 MultipartFile 实例
            return new MockMultipartFile(fileName,
                    fileName,
                    contentType,
                    inputStream);
        }
    }

    /*public MultipartFile urlToMultipartFile(String fileUrl, String fileName) throws IOException {
        // 打开URL连接并获取输入流
        URL url = new URL(fileUrl);
        try (InputStream inputStream = url.openStream()) {
            // 读取文件的内容类型
            String contentType = URLConnection.guessContentTypeFromStream(inputStream);

            // 如果无法自动检测内容类型，则可以手动指定
            if (contentType == null) {
                contentType = "application/octet-stream"; // 默认类型
            }

            // 回到流的开始位置以便重新读取
            inputStream.reset();

            // 使用 MockMultipartFile 创建 MultipartFile 实例
            return new MockMultipartFile(fileName,
                    fileName,
                    contentType,
                    inputStream);
        }
    }


    /**
     * 认证情况概览
     * 产品 样品状态维度
     */
    @Override
    public PageResult getManagerTable(TestProjectOutTestData param){
        LambdaQueryWrapper<TestProjectOutTestData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TestProjectOutTestData::getProductName);
        queryWrapper.isNotNull(TestProjectOutTestData::getPhase);
        if(StrUtil.isNotBlank(param.getProductName())){
            queryWrapper.like(TestProjectOutTestData::getProductName,param.getProductName());
        }
        if(StrUtil.isNotBlank(param.getPhase())){
            queryWrapper.like(TestProjectOutTestData::getPhase,param.getPhase());
        }
        queryWrapper.orderByAsc(TestProjectOutTestData::getProductName,TestProjectOutTestData::getPhase);
        List<TestProjectOutTestData> list = this.list(queryWrapper);

        List<JSONObject> resultList = new ArrayList<>();

        for (TestProjectOutTestData testData:list){
            Optional<JSONObject> first = resultList.stream().filter(r -> testData.getProductName().equals(r.getStr("productName"))
                    && testData.getPhase().equals(r.getStr("phase"))).findFirst();
            if(first.isPresent()){
                continue;
            }
            JSONObject testProjectOutTestTableResult = new JSONObject();
            testProjectOutTestTableResult.put("phase",testData.getPhase());
            testProjectOutTestTableResult.put("productName",testData.getProductName());
            //每个报告查询一次
            List<TestProjectOutTestData> testDataList = list.stream().filter(r -> testData.getProductName().equals(r.getProductName()) && testData.getPhase().equals(r.getPhase())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(testDataList)){
                fullData(testProjectOutTestTableResult,testDataList,"31484","gb31484");
                fullData(testProjectOutTestTableResult,testDataList,"38031","gb38031");
                fullData(testProjectOutTestTableResult,testDataList,"31486","gb31486");
                fullData(testProjectOutTestTableResult,testDataList,"31241","gb31241");
                fullData(testProjectOutTestTableResult,testDataList,"43854","gb43854");
                fullData(testProjectOutTestTableResult,testDataList,"UN38.3","un383");
                fullData(testProjectOutTestTableResult,testDataList,"UN38.3实验概要","un383Test");
                fullData(testProjectOutTestTableResult,testDataList,"空运","air");
                fullData(testProjectOutTestTableResult,testDataList,"海运","sea");
                fullData(testProjectOutTestTableResult,testDataList,"天路","rail");
                fullData(testProjectOutTestTableResult,testDataList,"公路","road");
                fullData(testProjectOutTestTableResult,testDataList,"MSDS","msds");
                fullData(testProjectOutTestTableResult,testDataList,"危险特性类别鉴定","dangerous");
                fullData(testProjectOutTestTableResult,testDataList,"62133","iec62133");
                fullData(testProjectOutTestTableResult,testDataList,"62619","iec62619");
                fullData(testProjectOutTestTableResult,testDataList,"62660","iec62660");
                fullData(testProjectOutTestTableResult,testDataList,"1642","ul1642");
                fullData(testProjectOutTestTableResult,testDataList,"1973","ul1973");
                fullData(testProjectOutTestTableResult,testDataList,"9540A","ul9540A");
                fullData(testProjectOutTestTableResult,testDataList,"16893-1","is168931");
                fullData(testProjectOutTestTableResult,testDataList,"16893-2","is168932");
            }
            resultList.add(testProjectOutTestTableResult);

        }

        //分页处理
        PageResult pageResult = new PageResult();
        pageResult.setPageNo(param.getPageNo());
        pageResult.setPageSize(param.getPageSize());

        List<JSONObject> collect = resultList.stream().skip(new Long((param.getPageNo() - 1) * param.getPageSize())).limit(new Long(param.getPageSize())).collect(Collectors.toList());
        List<TestProjectOutTestTableResult> rows = new ArrayList<>();
        for (int i = 0; i < collect.size(); i++) {
            rows.add(BeanUtil.copyProperties(collect.get(i),TestProjectOutTestTableResult.class));
        }

        pageResult.setRows(rows);
        pageResult.setTotalRows(resultList.size());
        pageResult.setTotalPage(PageUtil.totalPage(resultList.size(),
                param.getPageSize()));
        return pageResult;

    }

    @Override
    public Map echartsData(String date) {
        Map result = new HashMap<>();
        LambdaQueryWrapper<TestProjectOutTestData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TestProjectOutTestData::getFileName);
        queryWrapper.isNotNull(TestProjectOutTestData::getCostCenterCode);
        List<TestProjectOutTestData> list = this.list(queryWrapper);
        if(StrUtil.isNotBlank(date)){
            list = list.stream().filter(l -> null != l.getCertificateDate() &&
                    DateUtil.format(l.getCertificateDate(),"yyyyMM").contains(date)).collect(Collectors.toList());
        }

        Set<String> costCenterSet = new TreeSet<>();
        list.stream().forEach(s -> costCenterSet.add(s.getCostCenterCode()));

        List<Map> series = new ArrayList<>();
        Long otherNum = 0L;
        List<Long> totalNum = new ArrayList<>();
        for (String costCenter : costCenterSet) {
            Map serie = new HashMap();
            list.stream().filter(l -> null != l.getCostCenterCode() && l.getCostCenterCode().equals(costCenter)
                    ).sorted();

            // 过滤、倒序排序并找到第一个元素
            Optional<TestProjectOutTestData> first = list.stream()
                    .filter(l -> l.getCostCenterCode() != null && l.getCostCenterCode().equals(costCenter)) // 过滤条件
                    .sorted(Comparator.comparing(TestProjectOutTestData::getFolderNo).reversed())
                    .findFirst();
            serie.put("name", first.isPresent()?first.get().getCostCenter():costCenter);
            serie.put("type", "bar");
            serie.put("yAxisIndex", 0);
            List<Long> data = new ArrayList<>();
            List<TestProjectOutTestData> testDataList = list.stream().filter(t -> t.getCostCenterCode().equals(costCenter)).collect(Collectors.toList());
            //强检
            data.add(testDataList.stream().filter(t -> Arrays.asList("31484", "38031", "31486").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("31241", "43854").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("UN38.3", "UN38.3实验概要", "空运", "海运", "铁路", "公路", "MSDS","危险").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("62133", "62619", "62660").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("1642", "1973", "9540A").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("16046").stream().anyMatch(t.getTestProjectName()::contains)).count());
            data.add(testDataList.stream().filter(t -> Arrays.asList("16893-1", "16893-2").stream().anyMatch(t.getTestProjectName()::contains)).count());
            long sum = data.stream().mapToLong(Long::longValue).sum();
//            data.add(testDataList.size() - sum);
//            otherNum += testDataList.size() - sum;
            serie.put("data", data);
            series.add(serie);
        }

        //强检
        totalNum.add(list.stream().filter(t -> Arrays.asList("31484", "38031", "31486").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("31241", "43854").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("UN38.3", "UN38.3实验概要", "空运", "海运", "铁路", "公路", "MSDS","危险").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("62133", "62619", "62660").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("1642", "1973", "9540A").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("16046").stream().anyMatch(t.getTestProjectName()::contains)).count());
        totalNum.add(list.stream().filter(t -> Arrays.asList("16893-1", "16893-2").stream().anyMatch(t.getTestProjectName()::contains)).count());

//        totalNum.add(otherNum);

        Map serie = new HashMap();
        serie.put("name", "总计");
        serie.put("type", "line");
        serie.put("yAxisIndex", 1);
        serie.put("data", totalNum);
        series.add(serie);

        result.put("series", series);
        result.put("xData", Arrays.asList("强检","CCC","UN38.3","IEC","UL","BIS","IS16893"));

        return result;
    }

    /**
     * 认证报告 产品维度
     * @param param
     * @return
     */
    @Override
    public Map getManagerFileList(TestProjectOutTestData param){
        Map result = new HashMap<>();
        LambdaQueryWrapper<TestProjectOutTestData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(TestProjectOutTestData::getFileName);
        queryWrapper.isNotNull(TestProjectOutTestData::getPhase);
        if(StrUtil.isNotBlank(param.getProductName())){
            queryWrapper.like(TestProjectOutTestData::getProductName,param.getProductName());
        }

        queryWrapper.orderByAsc(TestProjectOutTestData::getProductName);
        List<TestProjectOutTestData> list = this.list(queryWrapper);

        //文件名处理
        list.forEach(l -> {
            if(StrUtil.isNotBlank(l.getFileName())){
                String name = l.getProductName() + "-";
                if(StrUtil.isNotBlank(l.getPhase())){
                    name += l.getPhase() + "-";
                }
                name += l.getTestProjectName();
                if(null != l.getPeriodValidity()){
                    name += "-" + DateUtil.format(l.getPeriodValidity(),"yyyyMMdd");
                }
                l.setTestReportCode(name);
            }
        });

        List<JSONObject> resultList = new ArrayList<>();

        for (TestProjectOutTestData testData:list){
            Optional<JSONObject> first = resultList.stream().filter(r -> testData.getProductName().equals(r.getStr("productName"))).findFirst();
            if(first.isPresent()){
                continue;
            }
            List<TestProjectOutTestData> testDataList = list.stream().filter(r -> testData.getProductName().equals(r.getProductName())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(testDataList)){

                JSONObject testProjectOutTestTableResult = new JSONObject();
                testProjectOutTestTableResult.put("id", IdUtil.fastSimpleUUID());
                testProjectOutTestTableResult.put("productName",testData.getProductName());
                List<JSONObject> objects = new ArrayList<>();
                //强检
                JSONObject jsonObject = fullFileData(testDataList, Arrays.asList("31484", "38031", "31486"), "强检", testProjectOutTestTableResult);
                if(jsonObject != null){
                    objects.add(jsonObject);
                }
                JSONObject jsonObject1 = fullFileData(testDataList, Arrays.asList("31241", "43854"), "CCC", testProjectOutTestTableResult);
                if(jsonObject1 != null){
                    objects.add(jsonObject1);
                }
                JSONObject jsonObject2 = fullFileData(testDataList, Arrays.asList("UN38.3", "UN38.3实验概要", "空运", "海运", "铁路", "公路", "MSDS", "危险"), "UN38.3", testProjectOutTestTableResult);
                if(jsonObject2 != null){
                    objects.add(jsonObject2);
                }
                JSONObject jsonObject3 = fullFileData(testDataList, Arrays.asList("62133", "62619", "62660"), "IEC", testProjectOutTestTableResult);
                if(jsonObject3 != null){
                    objects.add(jsonObject3);
                }
                JSONObject jsonObject4 = fullFileData(testDataList, Arrays.asList("1642", "1973", "9540A"), "UL", testProjectOutTestTableResult);
                if(jsonObject4 != null){
                    objects.add(jsonObject4);
                }
                JSONObject jsonObject5 = fullFileData(testDataList, Arrays.asList("16046"), "BIS", testProjectOutTestTableResult);
                if(jsonObject5 != null){
                    objects.add(jsonObject5);
                }
                JSONObject jsonObject6 = fullFileData(testDataList, Arrays.asList("16893-1", "16893-2"), "IS16893", testProjectOutTestTableResult);
                if(jsonObject6 != null){
                    objects.add(jsonObject6);
                }


                testProjectOutTestTableResult.put("list",objects);
                resultList.add(testProjectOutTestTableResult);
            }


        }

        result.put("list",resultList);

        long count = list.stream().filter(l -> {
            if (null != l.getPeriodValidity()) {
                LocalDate periodValidityDate = LocalDate.ofEpochDay(l.getPeriodValidity().getTime() / (24 * 60 * 60 * 1000));
                LocalDate today = LocalDate.now();
                long daysBetween = ChronoUnit.DAYS.between(today, periodValidityDate);
                if (daysBetween < 0) {
                    return true;
                }
            }
            return false;
        }).count();

        result.put("reportNum",list.size());
        result.put("validNum",list.size() - count);
        result.put("expireNum",count);

        return result;
    }

    @Override
    public Boolean update(TestProjectOutTestData param) {
        //上传报告
        if(null != param.getFileId() && new Integer(0).equals(param.getFileStatus())){
            TestProjectOutTestData outTestData = this.getById(param.getId());
            String fileIds = outTestData.getFileIds();
            String fileName = outTestData.getFileName();
            if(StrUtil.isNotBlank(fileIds)){
                fileIds += ","+param.getFileId();
            }else{
                fileIds = param.getFileId().toString();
            }
            if(StrUtil.isNotBlank(fileName)){
                //window不允许问号文件名
                fileName += "?"+param.getFileName();
            }else{
                fileName = param.getFileName();
            }
            param.setFileIds(fileIds);
            param.setFileName(fileName);
        }
        //上传证书
        if(null != param.getCertificateFileId() && new Integer(0).equals(param.getCertificateFileStatus())){
            TestProjectOutTestData outTestData = this.getById(param.getId());
            String fileIds = outTestData.getCertificateFileIds();
            String fileName = outTestData.getCertificateFileName();
            if(StrUtil.isNotBlank(fileIds)){
                fileIds += ","+param.getCertificateFileId();
            }else{
                fileIds = param.getCertificateFileId().toString();
            }
            if(StrUtil.isNotBlank(fileName)){
                //window不允许问号文件名
                fileName += "?"+param.getCertificateFileName();
            }else{
                fileName = param.getCertificateFileName();
            }
            param.setCertificateFileIds(fileIds);
            param.setCertificateFileName(fileName);
        }

        //删除报告
        if(null != param.getFileId() && new Integer(1).equals(param.getFileStatus())){
            TestProjectOutTestData outTestData = this.getById(param.getId());

            String fileIds = outTestData.getFileIds();
            String fileNames = outTestData.getFileName();

            if (StrUtil.isNotBlank(fileIds) && StrUtil.isNotBlank(fileNames)) {
                // 将fileIds和fileNames转换为列表
                List<String> fileIdList = new ArrayList<>(Arrays.asList(fileIds.split(",")));
                List<String> fileNameList = new ArrayList<>(Arrays.asList(fileNames.split("\\?")));

                // 找到要删除的fileId在列表中的位置
                int indexToRemove = -1;
                for (int i = 0; i < fileIdList.size(); i++) {
                    if (fileIdList.get(i).equals(param.getFileId().toString())) {
                        indexToRemove = i;
                        break;
                    }
                }

                // 如果找到了对应的fileId，则从两个列表中移除相应的元素
                if (indexToRemove != -1) {
                    fileIdList.remove(indexToRemove);
                    fileNameList.remove(indexToRemove);

                    // 更新数据
                    outTestData.setFileIds(String.join(",", fileIdList));
                    outTestData.setFileName(String.join("?", fileNameList));

                    // 保存更新后的数据（假设有一个update方法）
                    this.updateById(outTestData);
                } else {
                    throw new IllegalArgumentException("未找到指定的fileId");
                }
            } else {
                throw new IllegalArgumentException("fileIds 或 fileNames 为空");
            }
        }

        // 删除证书文件：根据传递进来的 param.getCertificateFileId()
        if (param.getCertificateFileId() != null && new Integer(1).equals(param.getCertificateFileStatus())) {
            TestProjectOutTestData outTestData = this.getById(param.getId());

            String fileIds = outTestData.getCertificateFileIds();
            String fileNames = outTestData.getCertificateFileName();

            if (StrUtil.isNotBlank(fileIds) && StrUtil.isNotBlank(fileNames)) {
                List<String> fileIdList = Arrays.asList(fileIds.split(","));
                List<String> fileNameList = Arrays.asList(fileNames.split("\\?"));

                // 创建可变列表用于操作
                List<String> mutableFileIds = new ArrayList<>(fileIdList);
                List<String> mutableFileNames = new ArrayList<>(fileNameList);

                boolean removed = false;
                for (int i = 0; i < mutableFileIds.size(); i++) {
                    if (param.getCertificateFileId().toString().equals(mutableFileIds.get(i))) {
                        // 删除 fileId 和 fileName 中对应位置的元素
                        mutableFileIds.remove(i);
                        mutableFileNames.remove(i);
                        removed = true;
                        break;
                    }
                }

                if (removed) {
                    // 更新参数对象
                    param.setCertificateFileIds(String.join(",", mutableFileIds));
                    param.setCertificateFileName(String.join("?", mutableFileNames));

                    // 如果需要更新数据库实体中的值，也可以加上：
                    // outTestData.setCertificateFileIds(param.getCertificateFileIds());
                    // outTestData.setCertificateFileName(param.getCertificateFileName());
                    // this.updateById(outTestData);
                } else {
                    throw new IllegalArgumentException("未找到指定的 certificateFileId：" + param.getCertificateFileId());
                }
            } else {
                // 如果原始数据为空，说明没有可删内容
                param.setCertificateFileIds("");
                param.setCertificateFileName("");
            }
        }

        return updateById(param);
    }

    public JSONObject fullFileData(List<TestProjectOutTestData> testDataList,List<String> typeList,String type,JSONObject testProjectOutTestTableResult){
        //强检
        List<TestProjectOutTestData> outTestDataList = testDataList.stream().filter(t -> typeList.stream().anyMatch(t.getTestProjectName()::contains))
                .sorted(Comparator.comparing(TestProjectOutTestData::getPhase,
                        Comparator.nullsLast(Comparator.naturalOrder())))
                .collect(Collectors.toList());

        JSONObject inRow = new JSONObject();
        inRow.put("id", IdUtil.fastSimpleUUID());
        inRow.set("type",type);
        List secondList = new ArrayList<>();
        //所有样品阶段
        if(CollUtil.isNotEmpty(outTestDataList)){

            Set<String> phaseSet = new TreeSet<>();
            outTestDataList.stream().forEach(s -> phaseSet.add(s.getPhase()));

            phaseSet.forEach(p -> {
                JSONObject secondInRow = new JSONObject();
                secondInRow.set("phase",p);
                secondInRow.put("id", IdUtil.fastSimpleUUID());
                //阶段不为空
                if(StrUtil.isNotBlank(p)){
                    List<TestProjectOutTestData> reportList = outTestDataList.stream().filter(s -> p.equals(s.getPhase())).collect(Collectors.toList());
                    List<TestProjectOutTestData> list = new ArrayList<>();
                    for (TestProjectOutTestData data : reportList) {
                        String fileIds = data.getFileIds();
                        String fileNames = data.getFileName();

                        if (StrUtil.isBlank(fileIds) || StrUtil.isBlank(fileNames)) {
                            continue;
                        }

                        List<String> idList = Arrays.asList(fileIds.split(","));
                        List<String> nameList = Arrays.asList(fileNames.split("\\?"));

                        int size = Math.min(idList.size(), nameList.size());

                        for (int i = 0; i < size; i++) {
                            TestProjectOutTestData copy = new TestProjectOutTestData();

                            // 一行代码复制所有属性（除了 fileId 和 fileName，后面单独设置）
                            BeanUtils.copyProperties(data, copy);

                            // 替换文件信息
                            copy.setFileId(Long.parseLong(idList.get(i)));
                            copy.setFileName(nameList.get(i));
                            copy.setId(data.getId()+i);

                            list.add(copy);
                        }
                    }

                    secondInRow.set("list",list);
                    secondList.add(secondInRow);
                }
            });


        }
        /*JSONObject secondInRow = new JSONObject();
        secondInRow.set("phase","未知");
        List<TestProjectOutTestData> noPhaseList = outTestDataList.stream().filter(s -> StrUtil.isBlank(s.getPhase())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(noPhaseList)){
            secondList.add(noPhaseList);
            secondList.add(secondInRow);
        }*/
        inRow.set("list",secondList);
        if(secondList.size() == 0){
            return null;
        }
        return inRow;
//        testProjectOutTestTableResult.put("list",inRow);
    }

    public void fullData(JSONObject result ,List<TestProjectOutTestData> testDataList,String key,String columnKey){
        List<TestProjectOutTestData> gb31484List = "UN38.3".equals(key) || "UN38.3实验概要".equals(key)?
                testDataList.stream().filter(t -> StrUtil.isNotBlank(t.getTestProjectName()) && t.getTestProjectName().equals(key))
                        .sorted(Comparator.comparing(TestProjectOutTestData::getPeriodValidity,
                                Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList()):
                testDataList.stream().filter(t -> StrUtil.isNotBlank(t.getTestProjectName()) && t.getTestProjectName().contains(key))
                        .sorted(Comparator.comparing(TestProjectOutTestData::getPeriodValidity,
                                Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                testDataList.stream().filter(t -> StrUtil.isNotBlank(t.getTestProjectName()) && t.getTestProjectName().contains(key))
                .sorted(Comparator.comparing(TestProjectOutTestData::getPeriodValidity,
                        Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(gb31484List)){
            TestProjectOutTestData testData1 = gb31484List.get(0);
            result.put(columnKey,DateUtil.format(testData1.getPeriodValidity(),"yyyy-MM-dd"));
            if(null != testData1.getPeriodValidity()){
                LocalDate periodValidityDate = LocalDate.ofEpochDay(testData1.getPeriodValidity().getTime() / (24 * 60 * 60 * 1000));
                LocalDate today = LocalDate.now();
                long daysBetween = ChronoUnit.DAYS.between(today, periodValidityDate);

                if (daysBetween < 0) {
                    result.put(columnKey+"Status","expire");
                } else if (daysBetween < 90) {
                    result.put(columnKey+"Status","nearExpire");
                } else {
                    result.put(columnKey+"Status","valid");
                }
            }else{
                result.put(columnKey+"Status","valid");
            }
        }
    }


}
