<template>
  <div :style="{padding: padding}">

    <tableIndex
      ref="pbiTableIndex"
      :pageLevel='1'
      :tableTotal='tableTotal'
      :pageTitleShow=false
      :loading='loading'
      :otherHeight="parseInt(58 + width)"
      @paginationChange="handlePageChange"
      @paginationSizeChange="handlePageChange"
    >
      <template #search>
        <pbiSearchContainer>
          <pbiSearchItem :span="4" label='任务名称'>
            <a-input v-model="queryparam.taskName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='任务类型'>
            <a-select v-model="queryparam.type" @change="getList(true)" style="width: 100%" :allow-clear="true">
              <a-select-option value="filter">
                在线数据提取
              </a-select-option>
              <a-select-option value="all">
                数据全量提取
              </a-select-option>
              <a-select-option value="excel">
                离线数据提取
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='任务状态'>
            <a-select v-model="queryparam.fileStatus" @change="getList(true)" style="width: 100%" :allow-clear="true">
              <a-select-option value="0">
                待处理
              </a-select-option>

              <a-select-option value="10">
                进行中
              </a-select-option>
              <a-select-option value="20">
                已完成
              </a-select-option>
            </a-select>
          </pbiSearchItem>
          <pbiSearchItem :span="4" label='创建人' v-if="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null">
            <a-input v-model="queryparam.createName" @keyup.enter="getList(true)" @change="getList(true)"/>
          </pbiSearchItem>

          <pbiSearchItem :span="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null?8:12" type='btn'>
            <a-button  style="margin-right: 12px;" @click="getList(true)" type="primary">查询</a-button>

            <a-popconfirm
              title="确定删除吗?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteData()"
              :visible="beforeDeleteFlag"
              @cancel="() => beforeDeleteFlag = false"
              placement="topRight">
              <a-button class="right-btn" style="margin-right: 12px;" @click="beforeDelete">
                删除
              </a-button>
            </a-popconfirm>
            <a-button   @click="reset" >重置</a-button>
          </pbiSearchItem>

        </pbiSearchContainer>
      </template>


      <template #table>
        <ag-grid-vue :style="{height:tableHeight}"
                     class='table ag-theme-balham'
                     :tooltipShowDelay="0"
                     :columnDefs="userInfo.account == 'superAdmin' || userInfo.roles.find(r => r.id == 1694647012972855298) != null?exportTaskAdminColumns:exportTaskcolumns"
                     :rowData='rowData'
                     rowSelection="multiple"
                     :gridOptions="gridOptions"
                     @grid-ready="onGridReady"
                     :defaultColDef='defaultColDef'>
        </ag-grid-vue>
      </template>
    </tableIndex>
  </div>
</template>

<script>
import {
  deleteTestDataExportTask,
  shenghongDataExportTaskPageList, shenghongDataExportTaskSave, shenghongDataRegenerate
} from '@/api/modular/system/limsManager'

import {
  getMinioDownloadUrl
} from '@/api/modular/system/fileManage'
import {mapGetters } from "vuex";
import axios from 'axios'
import {
  ACCESS_TOKEN
} from '@/store/mutation-types'
  import { STable } from '@/components'
import Vue from "vue";
import jsonBigint from 'json-bigint'

  export default {
    props: {
      width:{
        type: Number,
        default: 0
      },
      padding:{
        type: String,
        default: '8px'
      }
    },
    components: {
      STable,
      taskName: {
        template: '<a style="text-align: left" v-if="params.data.fileStatus == 20" @click="params.onClick(params.data,params.value)"> <a-spin :spinning="params.data.status == 1" tip="正在下载中" size="small">{{params.value}}</a-spin></a><div v-else style="text-align: left">{{params.value}}</div>'
      },
      actionHeader: {
        template: '<div style="text-align: center;width: 100%;"><span>操作 </span><a-tooltip placement="topLeft" title="刷新数据：在数据源以及数据处理逻辑不变的情况下，将最新的处理结果生成在数据处理页面，适用于循环。\n重新生成：可点击进原有数据的处理逻辑页面，进行测试数据的变更或者处理逻辑的变更。" arrow-point-at-center><a-icon class="tips" type="question-circle" /></a-tooltip></div>'
      },
      action:{
        template:'<div><a-popconfirm title="确定要刷新数据吗?" v-if="params.data.fileStatus != 10" ok-text="确定" cancel-text="取消" @confirm="params.onClick(params.data)" placement="topRight" :style="{marginRight: params.data.type != \'all\'?\'12px\':\'0px\'}"\><a>刷新数据</a></a-popconfirm><a-popconfirm v-if="params.data.type != \'all\'" title="确定要重新生成吗?" ok-text="确定" cancel-text="取消" @confirm="params.onReExport(params.data)" placement="topRight"><a>重新生成</a></a-popconfirm></div>'
      }
    },
    computed: {
      ...mapGetters(['userInfo','testTaskFilterData']),
    },
    data() {
      return {

        selectedRowKeys: [],
        selectedRows: [],
        beforeDeleteFlag: false,
        id:null,
        tableHeight: document.body.clientHeight - 156 - this.width +'px' ,
        defaultColDef: {
          filter: false,
          floatingFilter: false,
          editable: false,
          wrapText: true,
          autoHeight: false,
          width:100,
          minHeight: 40,
        },
        gridOptions: {
          onSelectionChanged: this.onSelectionChanged,
          suppressCellSelection: false,
          animateRows:false,
          suppressRowTransform :true,
          suppressRowHoverHighlight: true,
          suppressColumnVirtualisation: false,
          suppressRowVirtualisation: false,
          rowHeight: 40,
          rowBuffer: 10,
          suppressScrollOnNewData: true,
          suppressAnimationFrame: true,
          onGridReady: (event) => {
            event.api.sizeColumnsToFit();
          }
        },
        exportTaskAdminColumns: [
          {
            width: 40,
            sortable: false,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          }, {
            headerName: '任务名称',
            field: 'taskName',
            cellRenderer: 'taskName',
            width:200,
            cellRendererParams: {onClick: this.downloadFile},
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '任务类型',
            field: 'type',
            width: 105,
            cellRenderer: function (params) {

              if(params.data.type == 'filter'){
                return "在线数据提取"
              }else if(params.data.type == 'excel'){
                return "离线数据提取"
              }else {
                return "数据全量提取"
              }
            },
            tooltipValueGetter: (p) => {
              if(p.value == 'filter'){
                return "在线数据提取"
              }else if(p.value == 'excel'){
                return "离线数据提取"
              }else {
                return "数据全量提取"
              }
            }
          }, {
            headerName: '任务状态',
            field: 'fileStatus',
            cellRenderer: function (params) {
              if(params.data.fileStatus == 0){
                return "待处理"
              }
              if(params.data.fileStatus == 10){
                return "进行中"
              }
              if(params.data.fileStatus == 20){
                return "已完成"
              }
              if(params.data.fileStatus  == 30){
                //导出失败  展示为 新建
                return "待处理"
              }
            }
          }, {
            headerName: '排队位置',
            field: 'ranking',
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '文件大小',
            field: 'fileSizeInfo',
            tooltipValueGetter: (p) => p.value,
          },  {
            headerName: '创建人',
            field: 'createName',
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '创建时间',
            field: 'createTime',
            tooltipValueGetter: (p) => p.value,
          },{
            headerName: '开始时间',
            field: 'beginTime',
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '完成时间',
            field: 'finishTime',
            tooltipValueGetter: (p) => p.value,
          }, {

            headerComponent: 'actionHeader',
            cellRenderer: 'action',
            width:140,
            cellRendererParams: {onClick: this.refreshData, formatValue: this.formatValue,onReExport:this.reExport},
          }
        ],
        exportTaskcolumns: [
          {
            width: 40,
            checkboxSelection: true,
            headerCheckboxSelection:true
          },
          {
            headerName: '序号',
            field: 'id',
            width: 50,
            cellRenderer: function (params) {
              return parseInt(params.node.id) + 1
            }
          }, {
            headerName: '任务名称',
            field: 'taskName',
            width: 300,
            cellRenderer: 'taskName',
            flex: 2,
            cellRendererParams: {onClick: this.downloadFile},
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '任务类型',
            width: 110,
            field: 'type',
            cellRenderer: function (params) {

              if(params.data.type == 'filter'){
                return "在线数据处理"
              }else if(params.data.type == 'excel'){
                return "离线数据处理"
              }else {
                return "数据全量提取"
              }
            },
            tooltipValueGetter: (p) => {
              if(p.value == 'filter'){
                return "在线数据处理"
              }else if(p.value == 'excel'){
                return "离线数据处理"
              }else {
                return "数据全量提取"
              }
            },
          }, {
            headerName: '任务状态',
            width: 80,
            field: 'fileStatus',
            cellRenderer: function (params) {
              if(params.data.fileStatus == 0){
                return "待处理"
              }
              if(params.data.fileStatus == 10){
                return "进行中"
              }
              if(params.data.fileStatus == 20){
                return "已完成"
              }
              if(params.data.fileStatus  == 30){
                //导出失败  展示为 新建
                return "待处理"
              }
            },
            tooltipValueGetter: (p) => {
              if(p.data.fileStatus == 0){
                return "待处理"
              }
              if(p.data.fileStatus == 10){
                return "进行中"
              }
              if(p.data.fileStatus == 20){
                return "已完成"
              }
              if(p.data.fileStatus  == 30){
                return "待处理"
              }
            }
          }, {
            headerName: '排队位置',
            width: 80,
            field: 'ranking',
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '文件大小',
            width: 90,
            field: 'fileSizeInfo',
            tooltipValueGetter: (p) => p.value,
          },  {
            headerName: '创建时间',
            width: 135,
            field: 'createTime',
            tooltipValueGetter: (p) => p.value,
          },{
            headerName: '开始时间',
            width: 135,
            field: 'beginTime',
            tooltipValueGetter: (p) => p.value,
          }, {
            headerName: '完成时间',
            width: 135,
            field: 'finishTime',
            tooltipValueGetter: (p) => p.value,
          }, {

            width: 130,
            headerComponent: 'actionHeader',
            cellRenderer: 'action',
            cellRendererParams: {onClick: this.refreshData, formatValue: this.formatValue,onReExport:this.reExport},
          }
        ],
        rowData:[],
        tableTotal:0,
        record:{},
        loading: false,
        labelCol: {
          xs: {
            span: 12
          },
          sm: {
            span: 8
          }
        },
        wrapperCol: {
          xs: {
            span: 24
          },
          sm: {
            span: 14
          }
        },
        visible1: false,
        confirmLoading1: false,
        showExport:true,
        queryparam:{},
        pageNo: 1,
        pageSize: 20,
        gridApi: null,
        columnApi: null,
        
        
        /*headers: {
          //Authorization: 'Bearer ' + Vue.ls.get(ACCESS_TOKEN),
          Authorization: 'Bearer eyJhbGciOiJIUzUxMiJ9.***********************************************************************************************************************************************************************************************************************.LgAC7GwkaFwV5NJD7wrr4gQGYzFMfbJL46hei2EclvHWVE1WGgV8ucyJ5XkuF5Cio_24ZuIqwAyVXyD02snFmQ',
        },*/
      }
    },

    mounted() {

      this.loadData()
    },
    methods: {
      onSelectionChanged(event) {
        // 获取当前选中的行
        const selectedNodes = this.gridApi.getSelectedNodes();
        const selectedData = selectedNodes.map(node => node.data);

        // 更新选中的行数据
        this.selectedRows = selectedData;
        this.selectedRowKeys = selectedData;

      },
      onGridReady(params) {
        this.gridApi = params.api;
        this.columnApi = params.columnApi;
        // params.api.sizeColumnsToFit();
      },
      loadData() {
        this.loading = true
        this.queryparam.id = this.id
        return shenghongDataExportTaskPageList({
          ...{
            pageNo: this.pageNo,
            pageSize: this.pageSize
          }, ...this.queryparam
        }).then((res) => {
          if (res.success) {
            this.rowData = res.data.rows
            this.tableTotal = res.data.totalRows

          }
        }).finally(() => {
          if(this.rowData.length == 0 && this.pageNo > 1){
            // this.pageNo -= 1
            this.pageNo = Math.max(1, Math.ceil(this.tableTotal / this.pageSize))
            this.$refs.pbiTableIndex.$refs.pbiPagination.handleWithoutChange(this.pageNo,this.pageSize)
            this.loadData()
          }
          this.loading = false
        })
      },
      handlePageChange(value) {
        let {current, pageSize} = value
        this.pageNo = current
        this.pageSize = pageSize
        this.loadData()

      },
      async downloadFile(record,name) {
        if(record.fileIds){
          let fileIds = record.fileIds.split(",")
          for (let i = 0; i < fileIds.length; i++) {
            await getMinioDownloadUrl(fileIds[i]).then(res => {
              let fileUrl = res.data
              fileUrl = fileUrl.replace("http://***********:9000/", "/minioDownload/");
              const link = document.createElement('a');
              link.href = fileUrl;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link)

            })
          }
        }else{
          getMinioDownloadUrl(record.fileId).then(res => {
            if(res.data == null){
              record.status = 1
              let url = 'http://'+record.ip+(record.ip == '************' || record.ip == '***********'?':8088':':82')+'/sysFileInfo/downloadWithAuth'
              axios({
                url: url,
                method: 'POST',
                //headers:this.headers,
                data:{id:record.fileId,userAccount:this.userInfo.account,userName:this.userInfo.name},
                responseType: 'blob' // 设置响应类型为blob
              })
                .then(response => {
                  if(response.data.size < 500){
                    location.reload()
                    return
                  }
                  const url = window.URL.createObjectURL(new Blob([response.data]));
                  const link = document.createElement('a');
                  link.href = url;
                  link.setAttribute('download', name+'.xlsx'); // 设置下载文件的名称和扩展名
                  document.body.appendChild(link);
                  link.click();
                  record.status = 0
                })
                .catch(error => {
                  record.status = 0
                  //console.error(error);
                });
            }else{
              let fileUrl = res.data
              fileUrl = fileUrl.replace("http://***********:9000/", "/minioDownload/");
              const link = document.createElement('a');
              link.href = fileUrl;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link)
            }
          })
        }


      },
      beforeDelete() {
        if (this.selectedRows.length === 0) {
          this.$message.warning("请至少选择一条数据进行删除！")
        } else {
          this.beforeDeleteFlag = true
        }
      },
      deleteData() {
        this.selectedRows.forEach(item => {
          deleteTestDataExportTask({ id: item.id })
        })
        this.beforeDeleteFlag = false
        setTimeout(() => {
          this.selectedRows = []
          this.selectedRowKeys = []
          this.loadData()
        }, 800)
      },
      onSelectChange(selectedRowKeys, selectedRows) {
        // console.log('selectedRows',this.selectedRows)
        // console.log('selectedRowKeys',this.selectedRowKeys)
        this.selectedRows = selectedRows
        this.selectedRowKeys = selectedRowKeys
      },
      refreshData(record) {
        shenghongDataRegenerate(record).then((res) => {
          setTimeout(() => {
            this.loadData()
          }, 600)
        })
      },
      reExport(record){

        let json = jsonBigint({storeAsString:true})
        let param = json.parse(record.queryParam)

        /*console.log(param);*/

        /*let param = JSON.parse(record.queryParam);
        console.log(param)*/

        if(record.type == 'filter'){

          this.$store.commit('setTaskFilterData', param);
          this.$router.push('/testDataFilter');
          //this.$parent.showView(11,param)
        }else if(record.type == 'excel'){
          this.$store.commit('setTaskExcelData', param);
          this.$router.push('/testDataExcel');

          //this.$parent.showView(15,param)
        }else{

          let addParam = record
          addParam.beginTime = null
          addParam.createId = null
          addParam.createTime = null
          addParam.createName = null
          addParam.fileId = null
          addParam.fileStatus = null
          addParam.id = null
          addParam.updateTime = null
          addParam.finishTime = null

          shenghongDataExportTaskSave(addParam).then(res => {
            if (res.success) {
              this.loadData()
              this.$message.success('导出任务创建成功')

              //this.$parent.showView(12)
            } else {
              this.$message.warn(res.message)
            }
          })

        }




      },

      getList(flag){
        if(flag){
          this.id = null
        }
        this.loadData()
      },
      reset(){
        this.queryparam = {}
        this.loadData()
      }


    }
  }
</script>
<style lang="less" scoped>
@import '/src/components/pageTool/style/pbiSearchItem.less';
  .tips {
    color: #1890ff;
  }

  /deep/.ag-ltr .ag-cell {
    line-height: 1.5;
  }
  /deep/.ag-header-cell-label {
    text-overflow: clip;
    overflow: visible;
    white-space: normal;
  }
  /deep/.ag-cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 40px;
      padding:8px;
      display: flex;
      align-items: center;
      justify-content: center;
  }
  /deep/ .ag-cell-value,
  /deep/ .ag-cell-value div{
    text-align: center !important;
  }

  /deep/ .ag-ltr .ag-header-select-all,
  /deep/ .ag-ltr .ag-selection-checkbox{
    margin-right: 0;
  }

  /deep/ .ag-header-row .ag-header-cell:first-child .ag-header-cell-comp-wrapper{
    display: none;
  }

  /deep/ .ag-header-cell{
    display: flex;
    justify-content: center;
  }

   // 序号的高度
   /deep/.ag-cell-wrapper > *:not(.ag-cell-value):not(.ag-group-value){
    height: fit-content;
  }

</style>
