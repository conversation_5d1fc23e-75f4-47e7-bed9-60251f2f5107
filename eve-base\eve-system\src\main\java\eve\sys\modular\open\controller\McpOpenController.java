package eve.sys.modular.open.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import eve.core.annotion.BusinessLog;
import eve.core.enums.LogAnnotionOpTypeEnum;
import eve.core.pojo.page.PageResult;
import eve.core.pojo.response.ResponseData;
import eve.core.pojo.response.SuccessResponseData;
import eve.sys.dorisModular.testData.entity.Stepinfo;
import eve.sys.dorisModular.testData.service.IStepService;
import eve.sys.dorisModular.testData.service.IStepinfoService;
import eve.sys.jmDorisModular.jmData.entity.JmStepinfo;
import eve.sys.jmDorisModular.jmData.service.IJmStepinfoService;
import eve.sys.limsModular.folder.entity.TLimsFolder;
import eve.sys.limsModular.folder.service.ITLimsFolderService;
import eve.sys.limsModular.temGradient.entity.TLimsTemGradient;
import eve.sys.limsModular.temGradient.service.ITLimsTemGradientService;
import eve.sys.limsModular.testDataPrimary.entity.TLimsTestdataPrimary;
import eve.sys.limsModular.testDataPrimary.service.ITLimsTestdataPrimaryService;
import eve.sys.modular.labTestReport.entity.StandardReceiveFromJIRAParam;
import eve.sys.modular.labTestReport.service.impl.TestReportTodoTaskServiceImpl;
import eve.sys.modular.open.controller.param.TestFailureReceiveFromJIRAParam;
import eve.sys.modular.test.dpvTestFailure.service.IDpvTestFailureRecordService;
import eve.sys.modular.test.testReportTask.entity.TestReportTask;
import eve.sys.modular.test.testReportTask.param.dongLi.HlTempAIParam;
import eve.sys.modular.test.testReportTask.param.dongLi.HlTempOrCRateParam;
import eve.sys.modular.test.testReportTask.param.dongLi.HlTempOrCRateStepParam;
import eve.sys.modular.test.testReportTask.service.ITestReportTaskService;
import eve.sys.modular.test.testReportTask.service.impl.TestReportTaskServiceImpl;
import eve.sys.modular.topic.param.TopicListParam;
import eve.sys.modular.topicReviewMonth.service.ITopicReviewMonthService;
import eve.sys.mongoDbModular.shenghong.bean.HisStepInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/open/mcp")
@Api(tags = "mcp开放接口")
public class McpOpenController {

    @Resource
    private IStepinfoService stepinfoService;

    @Resource
    private IJmStepinfoService jmStepinfoService;

    @Resource
    private ITLimsTestdataPrimaryService primaryService;

    @Autowired
    private ITLimsTemGradientService tLimsTemGradientService;

    @Autowired
    private ITLimsFolderService folderService;

    @Autowired
    private ITestReportTaskService reportTaskService;


    @GetMapping("/getStepInfo")
    @ApiOperation("查询工步")
    public List<Stepinfo> receiveCheckedFrom(@RequestParam String flowId) throws IOException {
        LambdaQueryWrapper<Stepinfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Stepinfo::getFlowId,flowId);
        queryWrapper.orderByAsc(Stepinfo::getStepId);
        return stepinfoService.list(queryWrapper);
    }

    @GetMapping("/getStepInfoOnlyByFolderNoAndTestName")
    @ApiOperation("根据委托单号及测试项目名称获取工步")
    public List<Stepinfo> getStepInfoOnlyByFolderNoAndTestName(@RequestParam String folderNo, @RequestParam String testName) throws IOException {
        TLimsTestdataPrimary build = TLimsTestdataPrimary.builder().folderno(folderNo).alias(testName).pageNo(1).pageSize(100).build();
        PageResult<TLimsTestdataPrimary> tLimsTestdataPrimaryPageResult = primaryService.pageList(build);
        final List<Stepinfo>[] list = new List[]{new ArrayList<>()};
        tLimsTestdataPrimaryPageResult.getRows().forEach(r->{
            LambdaQueryWrapper<Stepinfo> queryWrapper = new LambdaQueryWrapper<>();
            Map<String,Object> map = JSON.parseObject(r.getQueryparam(), Map.class);
            List<String> values = new ArrayList<>();

            for (String key : map.keySet()) {
                values.add(map.get(key).toString());
            }
            String flowId = String.join("-", values);
            queryWrapper.eq(Stepinfo::getFlowId,flowId);
            queryWrapper.orderByAsc(Stepinfo::getStepId);
            List list1 = stepinfoService.list(queryWrapper);
            if(list1.size() > list[0].size()){
                list[0] = list1;
            }

        });

        if(list[0].size() == 0){
            tLimsTestdataPrimaryPageResult.getRows().forEach(r->{
                LambdaQueryWrapper<JmStepinfo> queryWrapper = new LambdaQueryWrapper<>();
                Map<String,Object> map = JSON.parseObject(r.getQueryparam(), Map.class);
                List<String> values = new ArrayList<>();

                for (String key : map.keySet()) {
                    values.add(map.get(key).toString());
                }
                String flowId = String.join("-", values);
                queryWrapper.eq(JmStepinfo::getFlowId,flowId);
                queryWrapper.orderByAsc(JmStepinfo::getStepId);
                List list1 = jmStepinfoService.list(queryWrapper);

                if(list1.size() > list[0].size()){
                    list[0] = list1;
                }

            });
        }
        System.err.println(JSON.toJSONString(list[0]));
        return list[0];
    }

    @GetMapping("/getStepInfoByFolderNoAndTestName")
    @ApiOperation("根据委托单号及测试项目名称获取工步")
    public String getStepInfoByFolderNoAndTestName(@RequestParam String folderNo, @RequestParam String testName) throws IOException {
        TLimsTestdataPrimary build = TLimsTestdataPrimary.builder().folderno(folderNo).alias(testName).pageNo(1).pageSize(100).build();
        PageResult<TLimsTestdataPrimary> tLimsTestdataPrimaryPageResult = primaryService.pageList(build);
        final List[] list = new List[]{new ArrayList<>()};
        tLimsTestdataPrimaryPageResult.getRows().forEach(r->{
            LambdaQueryWrapper<Stepinfo> queryWrapper = new LambdaQueryWrapper<>();
            Map<String,Object> map = JSON.parseObject(r.getQueryparam(), Map.class);
            List<String> values = new ArrayList<>();

            for (String key : map.keySet()) {
                values.add(map.get(key).toString());
            }
            String flowId = String.join("-", values);
            queryWrapper.eq(Stepinfo::getFlowId,flowId);
            queryWrapper.orderByAsc(Stepinfo::getStepId);
            List list1 = stepinfoService.list(queryWrapper);
            if(list1.size() > list[0].size()){
                list[0] = list1;
            }

        });

        if(list[0].size() == 0){
            tLimsTestdataPrimaryPageResult.getRows().forEach(r->{
                LambdaQueryWrapper<JmStepinfo> queryWrapper = new LambdaQueryWrapper<>();
                Map<String,Object> map = JSON.parseObject(r.getQueryparam(), Map.class);
                List<String> values = new ArrayList<>();

                for (String key : map.keySet()) {
                    values.add(map.get(key).toString());
                }
                String flowId = String.join("-", values);
                queryWrapper.eq(JmStepinfo::getFlowId,flowId);
                queryWrapper.orderByAsc(JmStepinfo::getStepId);
                List list1 = jmStepinfoService.list(queryWrapper);

                if(list1.size() > list[0].size()){
                    list[0] = list1;
                }

            });
        }
        System.err.println(JSON.toJSONString(list[0]));
        Map<String, String> param = new HashMap<>();
        param.put("conditionName", "测试内容");
        List<TLimsTemGradient> byOrdtaskId = tLimsTemGradientService.getByOrdtaskId(tLimsTestdataPrimaryPageResult.getRows().get(0).getOrdtaskid(), param);
        List<String> collect = byOrdtaskId.stream().map(TLimsTemGradient::getTemperature).collect(Collectors.toList());
        //没有25度则在最开头添加25
        if(!collect.contains("25")){
            collect.add(0,"25");
        }


        /*//示例  202507040204	 高低温放电4
        LambdaQueryWrapper<Stepinfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Stepinfo::getFlowId,"table308446e879ee7d624cbbbfe668fa66c0c572");
        queryWrapper.orderByAsc(Stepinfo::getStepId);
        List<Stepinfo> list1 = stepinfoService.list(queryWrapper);


        //示例高低温参数
        List<HlTempAIParam> hlTempParamList = new ArrayList<>();
        hlTempParamList.add(HlTempAIParam.builder().temp("25").ceStep("8").isStandard("1").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("45").ceStep("14").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("55").ceStep("22").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("0").ceStep("30").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("-10").ceStep("38").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("-20").ceStep("46").build());
        hlTempParamList.add(HlTempAIParam.builder().temp("-30").ceStep("54").build());*/

/*
        String example = "以下是202507040204 高低温放电4 的示例，工步为"+JSON.toJSONString(list1)+"，根据工步填写的高低温充放电报告的参数为，倍率值rate：0.33,充放电类型chOrDchType:Discharge"
                +JSON.toJSONString(hlTempParamList)+";参数解释为："+JSON.toJSONString(HlTempAIParam.builder().temp("充电/放电温度")
                .ceStep("充电/放电工步号,如是多个工步号，需传数值以逗号,隔开")
                .recordStep("详细数据工步号,详细数据工步号可不传，默认取当前容量&能量工步号的最后一个工步号,传数值")
                .isStandard("是否 容量&能量基准 1 是 0 否 且必须只有一个是基准1，需传1 或者 0 ").build());*/

        String example = "# 高低温充放电规则文档\r\n" + //
                        "\r\n" + //
                        "## 概述\r\n" + //
                        "\r\n" + //
                        "本文档描述了高低温放电测试的相关规则和参数配置，以202507040204高低温放电4为示例。\r\n" + //
                        "\r\n" + //
                        "## 基本参数\r\n" + //
                        "\r\n" + //
                        "- **倍率值 (rate)**: 0.33\r\n" + //
                        "- **充放电类型 (chOrDchType)**: Discharge\r\n" + //
                        "\r\n" + //
                        "## 工步配置\r\n" + //
                        "\r\n" + //
                        "### 完整工步序列\r\n" + //
                        "\r\n" + //
                        "以下是完整的工步配置，包含58个工步：\r\n" + //
                        "\r\n" + //
                        "| 工步ID | 工步名称 | 工步参数 | 截止条件 |\r\n" + //
                        "|--------|----------|----------|----------|\r\n" + //
                        "| 1 | 搁置 | - | 截止时间:00:01:00.000 |\r\n" + //
                        "| 2 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 3 | 搁置 | - | 截止时间:00:10:00.000;工步变量名称:;C100:1*28.7 |\r\n" + //
                        "| 4 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2.5 V |\r\n" + //
                        "| 5 | 搁置 | - | 截止时间:00:30:00.000 |\r\n" + //
                        "| 6 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 7 | 搁置 | - | 截止时间:00:30:00.000 |\r\n" + //
                        "| 8 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2.5 V |\r\n" + //
                        "| 9 | 搁置 | - | 截止时间:00:30:00.000 |\r\n" + //
                        "| 10 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 11 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 12 | 温箱配置 | 45,2() | - |\r\n" + //
                        "| 13 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 14 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2.5 V |\r\n" + //
                        "| 15 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 16 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 17 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 18 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 19 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 20 | 温箱配置 | 55,2() | - |\r\n" + //
                        "| 21 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 22 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2.5 V |\r\n" + //
                        "| 23 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 24 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 25 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 26 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 27 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 28 | 温箱配置 | 0,2() | - |\r\n" + //
                        "| 29 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 30 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2 V |\r\n" + //
                        "| 31 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 32 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 33 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 34 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 35 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 36 | 温箱配置 | -10,2() | - |\r\n" + //
                        "| 37 | 搁置 | - | 截止时间:05:00:00.000 |\r\n" + //
                        "| 38 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2 V |\r\n" + //
                        "| 39 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 40 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 41 | 搁置 | - | 截止时间:05:00:00.000 |\r\n" + //
                        "| 42 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 43 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 44 | 温箱配置 | -20,2() | - |\r\n" + //
                        "| 45 | 搁置 | - | 截止时间:05:00:00.000 |\r\n" + //
                        "| 46 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2 V |\r\n" + //
                        "| 47 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 48 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 49 | 搁置 | - | 截止时间:05:00:00.000 |\r\n" + //
                        "| 50 | 恒流恒压充电 | 4.25(V) 0.33*C100(A) | 截止时间:04:00:00.000;或\\|电流\\|<=\\|0.02*C100\\|A\\|下一步\\| |\r\n" + //
                        "| 51 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 52 | 温箱配置 | -30,2() | - |\r\n" + //
                        "| 53 | 搁置 | - | 截止时间:05:00:00.000 |\r\n" + //
                        "| 54 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2 V |\r\n" + //
                        "| 55 | 搁置 | - | 截止时间:00:00:01.000 |\r\n" + //
                        "| 56 | 温箱配置 | 25,2() | - |\r\n" + //
                        "| 57 | 搁置 | - | 截止时间:03:00:00.000 |\r\n" + //
                        "| 58 | 恒流放电 | 0.33*C100(A) | 截止时间:03:30:00.000;截止电压:2.5 V |\r\n" + //
                        "| 59 | 结束 | - | - |\r\n" + //
                        "\r\n" + //
                        "## 温度工步对应关系\r\n" + //
                        "\r\n" + //
                        "### 基准工步参数\r\n" + //
                        "\r\n" + //
                        "| 充放电工步号 | 是否基准 | 温度(°C) | 说明 |\r\n" + //
                        "|-------------|----------|----------|------|\r\n" + //
                        "| 8 | 1 (是) | 25 | 容量&能量基准 |\r\n" + //
                        "| 14 | 0 (否) | 45 | 高温测试 |\r\n" + //
                        "| 22 | 0 (否) | 55 | 高温测试 |\r\n" + //
                        "| 30 | 0 (否) | 0 | 低温测试 |\r\n" + //
                        "| 38 | 0 (否) | -10 | 低温测试 |\r\n" + //
                        "| 46 | 0 (否) | -20 | 低温测试 |\r\n" + //
                        "| 54 | 0 (否) | -30 | 低温测试 |\r\n" + //
                        "\r\n" + //
                        "### 参数说明\r\n" + //
                        "\r\n" + //
                        "- **ceStep**: 充电/放电工步号，如是多个工步号，需传数值以逗号分隔\r\n" + //
                        "- **isStandard**: 是否容量&能量基准 (1=是, 0=否)，必须只有一个是基准1\r\n" + //
                        "- **temp**: 充电/放电温度\r\n" + //
                        "\r\n" + //
                        "## 识别规则\r\n" + //
                        "\r\n" + //
                        "### 正确的识别步骤\r\n" + //
                        "\r\n" + //
                        "1. **温度预设**: 温度是提前给出来的（如：25°C, 45°C, 55°C, 0°C, -10°C, -20°C, -30°C）\r\n" + //
                        "\r\n" + //
                        "2. **读取工步信息**: 获取完整的工步序列并进行解析\r\n" + //
                        "\r\n" + //
                        "3. **判断充放电类型**: 确定当前测试是充电还是放电类型\r\n" + //
                        "\r\n" + //
                        "4. **温度匹配流程**:\r\n" + //
                        "   - 用每一个预设温度去匹配工步中的\"温箱配置\"\r\n" + //
                        "   - 找到对应温度的温箱配置工步\r\n" + //
                        "   - 根据充放电类型，从温箱配置工步往下查找对应的工步类型\r\n" + //
                        "\r\n" + //
                        "5. **工步号获取规则**:\r\n" + //
                        "   - **如果是充电**: 从温箱配置工步往下找，取第一个\"恒流充电\"或\"恒流恒压充电\"工步号\r\n" + //
                        "   - **如果是放电**: 从温箱配置工步往下找，取第一个\"恒流放电\"工步号\r\n" + //
                        "\r\n" + //
                        "### 示例说明\r\n" + //
                        "\r\n" + //
                        "以45°C放电为例：\r\n" + //
                        "1. 预设温度：45°C\r\n" + //
                        "2. 查找温箱配置：找到工步12 `温箱配置 | 45,2()`\r\n" + //
                        "3. 充放电类型：Discharge 放电\r\n" + //
                        "4. 从工步12往下查找：找到工步14 `恒流放电`\r\n" + //
                        "5. 结果：45°C放电对应工步号为14\r\n" + //
                        "\r\n" + //
                        "### 各温度对应的工步号（根据提供的数据）\r\n" + //
                        "\r\n" + //
                        "根据您提供的温度工步对应关系数据：\r\n" + //
                        "\r\n" + //
                        "| 温度 | 充放电工步号 | 是否基准 |\r\n" + //
                        "|------|-------------|----------|\r\n" + //
                        "| 25°C | 8 | 1 (是) |\r\n" + //
                        "| 45°C | 14 | 0 (否) |\r\n" + //
                        "| 55°C | 22 | 0 (否) |\r\n" + //
                        "| 0°C | 30 | 0 (否) |\r\n" + //
                        "| -10°C | 38 | 0 (否) |\r\n" + //
                        "| -20°C | 46 | 0 (否) |\r\n" + //
                        "| -30°C | 54 | 0 (否) |\r\n" + //
                        "\r\n" + //
                        "### 基准确定规则\r\n" + //
                        "\r\n" + //
                        "- 是否容量&能量基准：如果有25°C的优先以25°C为基准\r\n" + //
                        "- 必须只有一个温度设置为基准(isStandard=1)\r\n" + //
                        "\r\n" + //
                        "### 温度测试序列\r\n" + //
                        "\r\n" + //
                        "测试按以下温度顺序进行：\r\n" + //
                        "1. 25°C (基准温度)\r\n" + //
                        "2. 45°C (高温)\r\n" + //
                        "3. 55°C (高温)\r\n" + //
                        "4. 0°C (低温)\r\n" + //
                        "5. -10°C (低温)\r\n" + //
                        "6. -20°C (低温)\r\n" + //
                        "7. -30°C (低温)\r\n" + //
                        "\r\n" + //
                        "## 注意事项\r\n" + //
                        "\r\n" + //
                        "1. **温箱配置识别**: 通过温箱配置工步的参数（如\"45,2()\"）来识别温度\r\n" + //
                        "2. **工步顺序**: 必须从温箱配置工步往下顺序查找对应的充放电工步\r\n" + //
                        "3. **工步类型匹配**:\r\n" + //
                        "   - 充电：查找\"恒流充电\"或\"恒流恒压充电\"\r\n" + //
                        "   - 放电：查找\"恒流放电\"\r\n" + //
                        "4. **取第一个**: 从温箱配置往下找到的第一个匹配类型的工步即为目标工步\r\n" + //
                        "5. **基准设置**: 25°C必须设置为基准(isStandard=1)，其他温度设置为0\r\n" + //
                        "6. **工步间隔**: 每个温度测试前都有温箱配置工步和搁置工步\r\n" + //
                        "7. **截止条件**: 低温测试时截止电压通常为2V，常温和高温为2.5V\r\n" + //
                        "";

//        System.err.println(example);

        String need = "以下是"+folderNo+" "+testName+" 的工步为"+JSON.toJSONString(list[0])+",请根据工步填写的高低温充放电报告的参数;需要填写的温度如下："+JSON.toJSONString(collect)+
                "并识别出倍率值rate和充放电类型chOrDchType，充电为Charge,放电为Discharge；如果识别出是充电，对应温度的工步为充电相关的，如果是放电，对应温度的工步为放电相关的；温度请先读取温箱配置的工步进行匹配，然后再去找后面对应的工步。";
        System.err.println(example + need);

        return example + need;


    }

    @ApiOperation("生成报告")
    @PostMapping("/hlTempReport")
    public String hlTempReport(@RequestBody List<JSONObject> hlTempList,@RequestParam Double rate,@RequestParam String folderNo, @RequestParam String testName,@RequestParam String chOrDchType) throws IOException {

        System.err.println(JSON.toJSONString(hlTempList));
        System.err.println(rate);
        System.err.println(folderNo);
        System.err.println(testName);
        System.err.println(chOrDchType);
        TLimsTestdataPrimary build = TLimsTestdataPrimary.builder().folderno(folderNo).alias(testName).pageNo(1).pageSize(100).build();
        PageResult<TLimsTestdataPrimary> tLimsTestdataPrimaryPageResult = primaryService.pageList(build);

        HlTempOrCRateParam cRateParam = HlTempOrCRateParam.builder().orderDataList(tLimsTestdataPrimaryPageResult.getRows()).build();
        TLimsFolder limsFolder = folderService.getById(tLimsTestdataPrimaryPageResult.getRows().get(0).getFolderid());
        cRateParam.setProjectName(limsFolder.getProducttype());
        cRateParam.setPhase(limsFolder.getTechnicalstatus());
        cRateParam.setTempOrCRate(rate.toString());
        cRateParam.setYaxisMax("4");
        cRateParam.setYaxisMin("2");
        cRateParam.setChOrDchType(chOrDchType);
        List<HlTempOrCRateStepParam> stepParamList = new ArrayList<>();
        Integer standardIndex = 0;
        for (int i = 0; i < hlTempList.size(); i++) {
            HlTempOrCRateStepParam build1 = HlTempOrCRateStepParam.builder().tempOrCRate(hlTempList.get(i)
                            .getString("temp"))
                    .ceStep(hlTempList.get(i).getString("ceStep"))
                    .recordStep(hlTempList.get(i).getLong("recordStep"))
                    .build();

            String[] ceSteps = hlTempList.get(i).getString("ceStep").split(",");
            List<String> list = Arrays.asList(ceSteps);
            List<Long> listLong = list.stream().map(Long::parseLong).collect(Collectors.toList());
            build1.setCeStepList(listLong);
            stepParamList.add(build1);
            if("1".equals(hlTempList.get(i).getString("isStandard"))){
                standardIndex = i;
            }
        }
        cRateParam.setStandardIndex(standardIndex);
        cRateParam.setStepParamList(stepParamList);
        TestReportTask hlTemp = TestReportTask.builder().type("HlTemp").reportName(folderNo + "_" + testName + "_高低温充放电报告")
                .queryParam(JSON.toJSONString(cRateParam)).build();

        Boolean b = reportTaskService.commitDongLiParam(hlTemp);


        return "创建成功，可以在http://**********:88/v_report_online_manager查看";
    }


}

