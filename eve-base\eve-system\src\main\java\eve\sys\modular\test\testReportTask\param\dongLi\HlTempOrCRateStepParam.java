package eve.sys.modular.test.testReportTask.param.dongLi;

import lombok.*;
import java.util.List;

/**
 * <p>
 * 动力-高低温充放电或倍率充放电工步参数实体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HlTempOrCRateStepParam {

    /**
     * _uid
     */
    private String _uid;

    /**
     * 温度或倍率
     */
    private String tempOrCRate;

    /**
     * 容量&能量工步号
     */
    private String ceStep;

    /**
     * 容量&能量工步号列表
     */
    private List<Long> ceStepList;

    /**
     * 详细数据工步号
     */
    private Long recordStep;

}
